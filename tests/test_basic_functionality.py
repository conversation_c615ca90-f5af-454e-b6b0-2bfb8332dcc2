"""Basic functionality tests for the Sports Prediction Engine."""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from src.config import Config
from src.features.feature_engineering import FeatureEngineer
from src.models.prediction_models import LinearRegressionModel, PoissonModel


class TestFeatureEngineering:
    """Test feature engineering functionality."""
    
    def test_feature_engineer_initialization(self):
        """Test FeatureEngineer initialization."""
        engineer = FeatureEngineer()
        assert engineer.config is not None
    
    def test_prepare_model_features(self):
        """Test feature preparation for model input."""
        engineer = FeatureEngineer()
        
        # Mock feature dictionary
        features = {
            'home_points_per_game_last_5': 25.0,
            'away_points_per_game_last_5': 22.0,
            'home_points_allowed_per_game_last_5': 18.0,
            'away_points_allowed_per_game_last_5': 20.0,
            'home_red_zone_td_percentage': 0.6,
            'away_red_zone_td_percentage': 0.55,
            'home_third_down_conversion_rate': 0.4,
            'away_third_down_conversion_rate': 0.38,
            'home_pace_plays_per_game': 65.0,
            'away_pace_plays_per_game': 62.0,
            'home_field_advantage': 2.5,
            'weather_impact_factor': -0.1,
            'historical_avg_total': 45.0,
            'combined_offensive_strength': 23.5,
            'combined_defensive_strength': 19.0,
            'pace_differential': 3.0,
            'turnover_battle': 0.5
        }
        
        result = engineer.prepare_model_features(features)
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
        assert len(result.columns) == 17
    
    def test_weather_impact_calculation(self):
        """Test weather impact factor calculation."""
        engineer = FeatureEngineer()
        
        # Test cold weather
        cold_conditions = {'temperature': 25, 'wind_speed': 5}
        impact = engineer.get_weather_impact_factor(cold_conditions)
        assert impact < 0  # Should reduce scoring
        
        # Test high wind
        windy_conditions = {'temperature': 70, 'wind_speed': 20}
        impact = engineer.get_weather_impact_factor(windy_conditions)
        assert impact < 0  # Should reduce scoring
        
        # Test good conditions
        good_conditions = {'temperature': 72, 'wind_speed': 5}
        impact = engineer.get_weather_impact_factor(good_conditions)
        assert impact == 0  # Neutral impact


class TestPredictionModels:
    """Test prediction model functionality."""
    
    def test_linear_regression_model(self):
        """Test LinearRegressionModel."""
        model = LinearRegressionModel()
        
        # Create mock training data
        X = pd.DataFrame({
            'feature1': [1, 2, 3, 4, 5],
            'feature2': [2, 4, 6, 8, 10],
            'feature3': [0.5, 1.0, 1.5, 2.0, 2.5]
        })
        y = pd.Series([45, 50, 55, 60, 65])  # Total points
        
        # Train model
        metrics = model.train(X, y)
        
        assert model.is_trained
        assert 'mae' in metrics
        assert 'rmse' in metrics
        assert 'feature_importance' in metrics
        
        # Test prediction
        predictions = model.predict(X)
        assert len(predictions) == len(X)
        assert all(isinstance(p, (int, float, np.number)) for p in predictions)
    
    def test_poisson_model(self):
        """Test PoissonModel."""
        model = PoissonModel()
        
        # Create mock training data
        X = pd.DataFrame({
            'feature1': [1, 2, 3, 4, 5],
            'feature2': [2, 4, 6, 8, 10]
        })
        home_scores = pd.Series([24, 28, 21, 35, 31])
        away_scores = pd.Series([21, 22, 34, 25, 34])
        
        # Train model
        metrics = model.train(X, home_scores, away_scores)
        
        assert model.is_trained
        assert 'mae' in metrics
        assert 'rmse' in metrics
        
        # Test prediction
        predictions = model.predict(X)
        assert len(predictions) == len(X)
        assert all(p >= 0 for p in predictions)  # Non-negative predictions


class TestConfiguration:
    """Test configuration management."""
    
    def test_config_initialization(self):
        """Test Config class initialization."""
        config = Config()
        
        assert hasattr(config, 'DATABASE_URL')
        assert hasattr(config, 'MODEL_WEIGHTS')
        assert hasattr(config, 'OFFENSIVE_FEATURES')
        assert hasattr(config, 'DEFENSIVE_FEATURES')
        assert hasattr(config, 'SITUATIONAL_FEATURES')
    
    def test_model_weights_sum(self):
        """Test that model weights sum to 1.0."""
        config = Config()
        weights = config.MODEL_WEIGHTS
        
        total_weight = sum(weights.values())
        assert abs(total_weight - 1.0) < 0.001  # Allow for floating point precision


class TestDataValidation:
    """Test data validation and processing."""
    
    def test_feature_validation(self):
        """Test that features are properly validated."""
        engineer = FeatureEngineer()
        
        # Test with missing features
        incomplete_features = {
            'home_points_per_game_last_5': 25.0,
            'away_points_per_game_last_5': 22.0
            # Missing other required features
        }
        
        result = engineer.prepare_model_features(incomplete_features)
        
        # Should handle missing features by filling with 0
        assert isinstance(result, pd.DataFrame)
        assert len(result.columns) == 17
        
        # Check that missing features are filled with 0
        for col in result.columns:
            if col not in incomplete_features:
                assert result[col].iloc[0] == 0
    
    def test_prediction_bounds(self):
        """Test that predictions are within reasonable bounds."""
        # This would be expanded with actual model testing
        # For now, just test that we can create reasonable bounds
        
        min_reasonable_total = 10  # Very low scoring game
        max_reasonable_total = 80  # Very high scoring game
        
        # Mock prediction
        prediction = 45.5
        
        assert min_reasonable_total <= prediction <= max_reasonable_total


if __name__ == '__main__':
    pytest.main([__file__])

"""NFL Data Scraper using nfl-data-py and web scraping."""

import pandas as pd
import nfl_data_py as nfl
import requests
from bs4 import BeautifulSoup
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import uuid

from src.config import Config
from src.database.connection import db_manager
from src.database.models import Team, Game, TeamStats
from sqlalchemy import text

logger = logging.getLogger(__name__)


class NFLDataScraper:
    """Comprehensive NFL data scraper using multiple sources."""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.current_season = datetime.now().year
        self.current_week = self._get_current_week()
        
        # Set up session for web scraping
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def _get_current_week(self) -> int:
        """Calculate current NFL week based on date."""
        # NFL season typically starts first Thursday after Labor Day
        # This is a simplified calculation
        now = datetime.now()
        if now.month < 9:  # Before season
            return 1
        elif now.month > 12:  # After regular season
            return 18
        else:
            # Rough calculation - can be improved with actual NFL calendar
            week = ((now - datetime(now.year, 9, 1)).days // 7) + 1
            return min(max(week, 1), 18)
    
    def get_play_by_play_data(self, seasons: List[int] = None) -> pd.DataFrame:
        """Get play-by-play data using nfl-data-py."""
        if seasons is None:
            seasons = [self.current_season]
        
        try:
            logger.info(f"Fetching play-by-play data for seasons: {seasons}")
            pbp_data = nfl.import_pbp_data(seasons)
            logger.info(f"Retrieved {len(pbp_data)} play-by-play records")
            return pbp_data
        except Exception as e:
            logger.error(f"Failed to fetch play-by-play data: {e}")
            return pd.DataFrame()
    
    def get_team_stats_data(self, seasons: List[int] = None) -> pd.DataFrame:
        """Get team statistics using nfl-data-py."""
        if seasons is None:
            seasons = [self.current_season]
        
        try:
            logger.info(f"Fetching team stats for seasons: {seasons}")
            team_stats = nfl.import_team_stats(seasons)
            logger.info(f"Retrieved team stats for {len(team_stats)} team-seasons")
            return team_stats
        except Exception as e:
            logger.error(f"Failed to fetch team stats: {e}")
            return pd.DataFrame()
    
    def get_schedule_data(self, seasons: List[int] = None) -> pd.DataFrame:
        """Get schedule data using nfl-data-py."""
        if seasons is None:
            seasons = [self.current_season]
        
        try:
            logger.info(f"Fetching schedule data for seasons: {seasons}")
            schedule = nfl.import_schedules(seasons)
            logger.info(f"Retrieved {len(schedule)} games")
            return schedule
        except Exception as e:
            logger.error(f"Failed to fetch schedule data: {e}")
            return pd.DataFrame()
    
    def scrape_injury_report(self) -> pd.DataFrame:
        """Scrape current injury reports from NFL.com and ESPN."""
        injuries = []

        try:
            # Method 1: NFL.com injury reports
            injuries.extend(self._scrape_nfl_injuries())

            # Method 2: ESPN injury reports (backup/additional data)
            injuries.extend(self._scrape_espn_injuries())

            # Method 3: Use nfl-data-py for injury data if available
            try:
                nfl_injuries = nfl.import_injuries([self.current_season])
                if not nfl_injuries.empty:
                    for _, row in nfl_injuries.iterrows():
                        injuries.append({
                            'player_name': row.get('full_name', ''),
                            'team': row.get('team', ''),
                            'position': row.get('position', ''),
                            'injury_status': row.get('report_status', ''),
                            'injury_description': row.get('report_description', ''),
                            'date_updated': row.get('date_modified', ''),
                            'source': 'nfl-data-py'
                        })
            except Exception as e:
                logger.warning(f"Could not fetch nfl-data-py injuries: {e}")

            df = pd.DataFrame(injuries)
            if not df.empty:
                # Remove duplicates based on player name and team
                df = df.drop_duplicates(subset=['player_name', 'team'], keep='first')
                logger.info(f"Scraped {len(df)} injury reports")

            return df

        except Exception as e:
            logger.error(f"Failed to scrape injury reports: {e}")
            return pd.DataFrame()

    def _scrape_nfl_injuries(self) -> List[Dict]:
        """Scrape injury reports from NFL.com."""
        injuries = []

        try:
            url = "https://www.nfl.com/injuries/"
            response = self.session.get(url)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Look for injury report tables or sections
            # NFL.com structure: Look for team sections and player rows
            team_sections = soup.find_all('div', class_=['nfl-o-teamhub-roster__team-name', 'team-injury-report'])

            if not team_sections:
                # Try alternative selectors
                team_sections = soup.find_all('section', attrs={'data-module': 'InjuryReport'})

            current_team = None

            for section in team_sections:
                # Extract team name
                team_header = section.find(['h2', 'h3', 'div'], class_=['team-name', 'nfl-c-team-name'])
                if team_header:
                    current_team = team_header.get_text(strip=True)

                # Find player injury rows
                player_rows = section.find_all(['tr', 'div'], class_=['player-row', 'injury-player'])

                for row in player_rows:
                    try:
                        # Extract player information
                        player_name_elem = row.find(['td', 'div', 'span'], class_=['player-name', 'name'])
                        position_elem = row.find(['td', 'div', 'span'], class_=['position', 'pos'])
                        injury_elem = row.find(['td', 'div', 'span'], class_=['injury', 'injury-description'])
                        status_elem = row.find(['td', 'div', 'span'], class_=['status', 'game-status'])

                        if player_name_elem:
                            player_name = player_name_elem.get_text(strip=True)
                            position = position_elem.get_text(strip=True) if position_elem else ''
                            injury_desc = injury_elem.get_text(strip=True) if injury_elem else ''
                            status = status_elem.get_text(strip=True) if status_elem else ''

                            injuries.append({
                                'player_name': player_name,
                                'team': current_team or 'Unknown',
                                'position': position,
                                'injury_status': status,
                                'injury_description': injury_desc,
                                'date_updated': datetime.now().strftime('%Y-%m-%d'),
                                'source': 'nfl.com'
                            })

                    except Exception as e:
                        logger.debug(f"Error parsing injury row: {e}")
                        continue

            time.sleep(self.config.SCRAPING_DELAY_SECONDS)

        except Exception as e:
            logger.error(f"Failed to scrape NFL.com injuries: {e}")

        return injuries

    def _scrape_espn_injuries(self) -> List[Dict]:
        """Scrape injury reports from ESPN as backup source."""
        injuries = []

        try:
            url = "https://www.espn.com/nfl/injuries"
            response = self.session.get(url)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # ESPN structure: Look for injury tables
            injury_tables = soup.find_all('div', class_=['ResponsiveTable', 'injuries'])

            for table in injury_tables:
                # Find team name
                team_header = table.find_previous(['h2', 'h3'], class_=['team-name', 'Table__Title'])
                team_name = team_header.get_text(strip=True) if team_header else 'Unknown'

                # Find player rows
                rows = table.find_all('tr', class_=['Table__TR'])

                for row in rows[1:]:  # Skip header row
                    try:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 4:
                            player_name = cells[0].get_text(strip=True)
                            position = cells[1].get_text(strip=True)
                            injury_desc = cells[2].get_text(strip=True)
                            status = cells[3].get_text(strip=True)

                            injuries.append({
                                'player_name': player_name,
                                'team': team_name,
                                'position': position,
                                'injury_status': status,
                                'injury_description': injury_desc,
                                'date_updated': datetime.now().strftime('%Y-%m-%d'),
                                'source': 'espn.com'
                            })

                    except Exception as e:
                        logger.debug(f"Error parsing ESPN injury row: {e}")
                        continue

            time.sleep(self.config.SCRAPING_DELAY_SECONDS)

        except Exception as e:
            logger.error(f"Failed to scrape ESPN injuries: {e}")

        return injuries
    
    def get_weather_data(self, game_id: str = None, stadium: str = None, game_date: str = None) -> Dict:
        """Get weather data for a specific game using multiple sources."""
        try:
            weather_data = {
                'temperature': None,
                'wind_speed': None,
                'conditions': None,
                'precipitation': None,
                'humidity': None,
                'dome_stadium': False
            }

            # Method 1: Check if it's a dome stadium (no weather impact)
            dome_stadiums = {
                'Mercedes-Benz Stadium', 'State Farm Stadium', 'AT&T Stadium',
                'Ford Field', 'NRG Stadium', 'Lucas Oil Stadium', 'Caesars Superdome',
                'U.S. Bank Stadium', 'Allegiant Stadium', 'SoFi Stadium'
            }

            if stadium and any(dome in stadium for dome in dome_stadiums):
                weather_data['dome_stadium'] = True
                weather_data['temperature'] = 72  # Controlled environment
                weather_data['wind_speed'] = 0
                weather_data['conditions'] = 'Indoor'
                return weather_data

            # Method 2: Try to get weather from nfl-data-py if available
            try:
                if game_id:
                    # nfl-data-py sometimes includes weather data in play-by-play
                    pbp_data = nfl.import_pbp_data([self.current_season])
                    game_weather = pbp_data[pbp_data['game_id'] == game_id]

                    if not game_weather.empty:
                        weather_info = game_weather.iloc[0]
                        weather_data.update({
                            'temperature': weather_info.get('temp'),
                            'wind_speed': weather_info.get('wind'),
                            'conditions': weather_info.get('weather', '').strip(),
                        })

                        if weather_data['temperature'] or weather_data['wind_speed']:
                            return weather_data
            except Exception as e:
                logger.debug(f"Could not get weather from nfl-data-py: {e}")

            # Method 3: Scrape weather from ESPN or NFL.com
            if stadium and game_date:
                weather_data.update(self._scrape_game_weather(stadium, game_date))

            # Method 4: Use OpenWeatherMap API (free tier) if configured
            weather_data.update(self._get_openweather_data(stadium, game_date))

            return weather_data

        except Exception as e:
            logger.error(f"Failed to get weather data for game {game_id}: {e}")
            return {
                'temperature': None,
                'wind_speed': None,
                'conditions': None,
                'precipitation': None,
                'humidity': None,
                'dome_stadium': False
            }

    def _scrape_game_weather(self, stadium: str, game_date: str) -> Dict:
        """Scrape weather data from sports websites."""
        weather_data = {}

        try:
            # Try ESPN first
            date_obj = datetime.strptime(game_date, '%Y-%m-%d')
            formatted_date = date_obj.strftime('%Y%m%d')

            # ESPN weather is often included in game pages
            espn_url = f"https://www.espn.com/nfl/game/_/gameId/{formatted_date}"
            response = self.session.get(espn_url)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Look for weather information
                weather_elem = soup.find('div', class_=['weather', 'game-weather'])
                if not weather_elem:
                    weather_elem = soup.find('span', string=lambda text: text and ('°' in text or 'mph' in text))

                if weather_elem:
                    weather_text = weather_elem.get_text(strip=True)
                    weather_data.update(self._parse_weather_text(weather_text))

            time.sleep(self.config.SCRAPING_DELAY_SECONDS)

        except Exception as e:
            logger.debug(f"Could not scrape weather from ESPN: {e}")

        return weather_data

    def _get_openweather_data(self, stadium: str, game_date: str) -> Dict:
        """Get weather data from OpenWeatherMap API (free tier)."""
        weather_data = {}

        try:
            # Stadium coordinates mapping (major NFL stadiums)
            stadium_coords = {
                'Lambeau Field': (44.5013, -88.0622),
                'Soldier Field': (41.8623, -87.6167),
                'Arrowhead Stadium': (39.0489, -94.4839),
                'Mile High Stadium': (39.7439, -105.0201),
                'Gillette Stadium': (42.0909, -71.2643),
                'MetLife Stadium': (40.8135, -74.0745),
                # Add more as needed
            }

            # OpenWeatherMap free API key (you'd need to set this in environment)
            api_key = os.getenv('OPENWEATHER_API_KEY')
            if not api_key or stadium not in stadium_coords:
                return weather_data

            lat, lon = stadium_coords[stadium]

            # For historical data, use the historical weather API
            # For future games, use current weather as approximation
            date_obj = datetime.strptime(game_date, '%Y-%m-%d')
            now = datetime.now()

            if date_obj < now - timedelta(days=5):
                # Historical weather (requires paid plan for OpenWeather)
                # For free tier, we'll skip historical data
                return weather_data
            else:
                # Current weather
                url = f"http://api.openweathermap.org/data/2.5/weather?lat={lat}&lon={lon}&appid={api_key}&units=imperial"
                response = self.session.get(url)

                if response.status_code == 200:
                    data = response.json()
                    weather_data.update({
                        'temperature': data['main'].get('temp'),
                        'wind_speed': data['wind'].get('speed'),
                        'conditions': data['weather'][0].get('description', '').title(),
                        'humidity': data['main'].get('humidity'),
                        'precipitation': data.get('rain', {}).get('1h', 0) + data.get('snow', {}).get('1h', 0)
                    })

        except Exception as e:
            logger.debug(f"Could not get OpenWeather data: {e}")

        return weather_data

    def _parse_weather_text(self, weather_text: str) -> Dict:
        """Parse weather information from text."""
        weather_data = {}

        try:
            import re

            # Extract temperature
            temp_match = re.search(r'(\d+)°?[F]?', weather_text)
            if temp_match:
                weather_data['temperature'] = int(temp_match.group(1))

            # Extract wind speed
            wind_match = re.search(r'(\d+)\s*mph', weather_text, re.IGNORECASE)
            if wind_match:
                weather_data['wind_speed'] = int(wind_match.group(1))

            # Extract conditions
            conditions = ['sunny', 'cloudy', 'rain', 'snow', 'fog', 'clear', 'overcast', 'partly cloudy']
            for condition in conditions:
                if condition.lower() in weather_text.lower():
                    weather_data['conditions'] = condition.title()
                    break

        except Exception as e:
            logger.debug(f"Error parsing weather text: {e}")

        return weather_data
    
    def process_and_store_team_stats(self, seasons: List[int] = None) -> None:
        """Process team statistics and store in database."""
        team_stats_df = self.get_team_stats_data(seasons)
        
        if team_stats_df.empty:
            logger.warning("No team stats data to process")
            return
        
        try:
            with db_manager.get_session() as session:
                for _, row in team_stats_df.iterrows():
                    # Check if record already exists
                    existing = session.query(TeamStats).filter_by(
                        team_id=row.get('team'),
                        season=row.get('season'),
                        week=row.get('week', 0)
                    ).first()
                    
                    if existing:
                        # Update existing record
                        for column in team_stats_df.columns:
                            if hasattr(existing, column) and column in row:
                                setattr(existing, column, row[column])
                    else:
                        # Create new record
                        team_stat = TeamStats(
                            team_id=row.get('team'),
                            season=row.get('season'),
                            week=row.get('week', 0),
                            points_scored=row.get('points_for'),
                            points_allowed=row.get('points_against'),
                            total_yards=row.get('total_yards'),
                            passing_yards=row.get('passing_yards'),
                            rushing_yards=row.get('rushing_yards'),
                            red_zone_pct=row.get('red_zone_pct'),
                            third_down_pct=row.get('third_down_pct'),
                            turnovers=row.get('turnovers'),
                            yards_allowed=row.get('yards_against'),
                            forced_turnovers=row.get('takeaways'),
                            yards_per_play=row.get('yards_per_play'),
                        )
                        session.add(team_stat)
                
                logger.info(f"Processed and stored team stats for {len(team_stats_df)} records")
                
        except Exception as e:
            logger.error(f"Failed to process and store team stats: {e}")
            raise
    
    def process_and_store_games(self, seasons: List[int] = None) -> None:
        """Process game schedule and store in database."""
        schedule_df = self.get_schedule_data(seasons)
        
        if schedule_df.empty:
            logger.warning("No schedule data to process")
            return
        
        try:
            with db_manager.get_session() as session:
                for _, row in schedule_df.iterrows():
                    game_id = row.get('game_id')
                    
                    # Check if game already exists
                    existing = session.query(Game).filter_by(game_id=game_id).first()
                    
                    if existing:
                        # Update existing game
                        existing.home_score = row.get('home_score')
                        existing.away_score = row.get('away_score')
                        existing.is_completed = pd.notna(row.get('home_score'))
                        if existing.is_completed:
                            existing.actual_total = (row.get('home_score', 0) + 
                                                   row.get('away_score', 0))
                    else:
                        # Create new game
                        game = Game(
                            game_id=game_id,
                            home_team_id=row.get('home_team'),
                            away_team_id=row.get('away_team'),
                            game_date=pd.to_datetime(row.get('gameday')),
                            season=row.get('season'),
                            week=row.get('week'),
                            home_score=row.get('home_score'),
                            away_score=row.get('away_score'),
                            is_completed=pd.notna(row.get('home_score'))
                        )
                        
                        if game.is_completed:
                            game.actual_total = (row.get('home_score', 0) + 
                                               row.get('away_score', 0))
                        
                        session.add(game)
                
                logger.info(f"Processed and stored {len(schedule_df)} games")
                
        except Exception as e:
            logger.error(f"Failed to process and store games: {e}")
            raise
    
    def update_current_season_data(self) -> None:
        """Update data for the current season."""
        logger.info(f"Updating current season data (Season: {self.current_season}, Week: {self.current_week})")
        
        # Update team stats
        self.process_and_store_team_stats([self.current_season])
        
        # Update games
        self.process_and_store_games([self.current_season])
        
        logger.info("Current season data update completed")
    
    def backfill_historical_data(self, start_season: int = 2020) -> None:
        """Backfill historical data from specified season to current."""
        seasons = list(range(start_season, self.current_season))
        
        logger.info(f"Backfilling historical data for seasons: {seasons}")
        
        # Process in batches to avoid overwhelming the API
        batch_size = 3
        for i in range(0, len(seasons), batch_size):
            batch = seasons[i:i + batch_size]
            
            logger.info(f"Processing batch: {batch}")
            
            # Update team stats
            self.process_and_store_team_stats(batch)
            
            # Update games
            self.process_and_store_games(batch)
            
            # Rate limiting
            time.sleep(2)
        
        logger.info("Historical data backfill completed")

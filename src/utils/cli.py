"""Command-line interface for the Sports Prediction Engine."""

import click
import logging
from datetime import datetime

from src.database.init_db import initialize_database
from src.data.nfl_scraper import NFLDataScraper
from src.models.prediction_models import EnsemblePredictionModel
from src.features.feature_engineering import FeatureEngineer
from src.api.app import create_app

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@click.group()
def cli():
    """Sports Over/Under Prediction Engine CLI."""
    pass


@cli.command()
def init_db():
    """Initialize the database with tables and seed data."""
    click.echo("Initializing database...")
    try:
        initialize_database()
        click.echo("✅ Database initialized successfully!")
    except Exception as e:
        click.echo(f"❌ Database initialization failed: {e}")


@cli.command()
@click.option('--type', 'update_type', default='current', 
              type=click.Choice(['current', 'historical']),
              help='Type of data update to perform')
@click.option('--start-season', default=2020, type=int,
              help='Starting season for historical data (only for historical type)')
def update_data(update_type, start_season):
    """Update NFL data."""
    click.echo(f"Updating {update_type} data...")
    
    try:
        scraper = NFLDataScraper()
        
        if update_type == 'current':
            scraper.update_current_season_data()
            click.echo("✅ Current season data updated successfully!")
        else:
            scraper.backfill_historical_data(start_season)
            click.echo(f"✅ Historical data updated from {start_season}!")
            
    except Exception as e:
        click.echo(f"❌ Data update failed: {e}")


@cli.command()
def train_model():
    """Train the prediction model."""
    click.echo("Training prediction model...")
    
    try:
        model = EnsemblePredictionModel()
        metrics = model.train()
        
        click.echo("✅ Model training completed!")
        click.echo(f"Training samples: {metrics['training_samples']}")
        click.echo(f"Ensemble MAE: {metrics['ensemble_mae']:.2f}")
        click.echo(f"Ensemble RMSE: {metrics['ensemble_rmse']:.2f}")
        
        # Save the trained model
        model.save_model('models/trained_model.pkl')
        click.echo("✅ Model saved to models/trained_model.pkl")
        
    except Exception as e:
        click.echo(f"❌ Model training failed: {e}")


@cli.command()
@click.option('--home', required=True, help='Home team abbreviation (e.g., KC)')
@click.option('--away', required=True, help='Away team abbreviation (e.g., BUF)')
@click.option('--season', default=None, type=int, help='Season year')
@click.option('--week', default=None, type=int, help='Week number')
def predict(home, away, season, week):
    """Make a prediction for a specific game."""
    if season is None:
        season = datetime.now().year
    if week is None:
        # Simple week calculation
        week = ((datetime.now() - datetime(datetime.now().year, 9, 1)).days // 7) + 1
        week = min(max(week, 1), 18)
    
    click.echo(f"Generating prediction for {away} @ {home} (Season {season}, Week {week})")
    
    try:
        # Load or train model
        model = EnsemblePredictionModel()
        try:
            model.load_model('models/trained_model.pkl')
            click.echo("✅ Loaded pre-trained model")
        except:
            click.echo("⚠️  No pre-trained model found, training new model...")
            model.train()
        
        # Extract features
        feature_engineer = FeatureEngineer()
        features_dict = feature_engineer.extract_game_features(home, away, season, week)
        features_df = feature_engineer.prepare_model_features(features_dict)
        
        # Make prediction
        result = model.predict_game(features_df, home, away)
        
        # Display results
        click.echo("\n" + "="*50)
        click.echo(f"PREDICTION: {away} @ {home}")
        click.echo("="*50)
        click.echo(f"Predicted Total: {result['predicted_total']} points")
        click.echo(f"Recommendation: {result['recommendation']}")
        click.echo(f"Confidence: {result['confidence_score']:.1%}")
        click.echo(f"\nConfidence Intervals:")
        click.echo(f"  80%: {result['confidence_80'][0]} - {result['confidence_80'][1]}")
        click.echo(f"  90%: {result['confidence_90'][0]} - {result['confidence_90'][1]}")
        click.echo(f"  95%: {result['confidence_95'][0]} - {result['confidence_95'][1]}")
        click.echo(f"\nModel Breakdown:")
        for model_name, prediction in result['model_breakdown'].items():
            click.echo(f"  {model_name}: {prediction}")
        click.echo(f"\nReasoning: {result['reasoning']}")
        click.echo("="*50)
        
    except Exception as e:
        click.echo(f"❌ Prediction failed: {e}")


@cli.command()
@click.option('--host', default='0.0.0.0', help='Host to bind to')
@click.option('--port', default=5000, type=int, help='Port to bind to')
@click.option('--debug', is_flag=True, help='Enable debug mode')
def serve(host, port, debug):
    """Start the API server."""
    click.echo(f"Starting API server on {host}:{port}")
    
    try:
        app = create_app('development' if debug else 'production')
        app.run(host=host, port=port, debug=debug)
    except Exception as e:
        click.echo(f"❌ Failed to start server: {e}")


@cli.command()
def setup():
    """Complete setup: initialize database, update data, and train model."""
    click.echo("🚀 Starting complete setup...")
    
    # Initialize database
    click.echo("\n1. Initializing database...")
    try:
        initialize_database()
        click.echo("✅ Database initialized")
    except Exception as e:
        click.echo(f"❌ Database initialization failed: {e}")
        return
    
    # Update current data
    click.echo("\n2. Updating current season data...")
    try:
        scraper = NFLDataScraper()
        scraper.update_current_season_data()
        click.echo("✅ Current data updated")
    except Exception as e:
        click.echo(f"⚠️  Data update failed: {e}")
    
    # Train model
    click.echo("\n3. Training prediction model...")
    try:
        model = EnsemblePredictionModel()
        metrics = model.train()
        model.save_model('models/trained_model.pkl')
        click.echo(f"✅ Model trained (MAE: {metrics['ensemble_mae']:.2f})")
    except Exception as e:
        click.echo(f"❌ Model training failed: {e}")
        return
    
    click.echo("\n🎉 Setup completed successfully!")
    click.echo("You can now:")
    click.echo("  • Make predictions: python -m src.utils.cli predict --home KC --away BUF")
    click.echo("  • Start API server: python -m src.utils.cli serve")


if __name__ == '__main__':
    cli()
